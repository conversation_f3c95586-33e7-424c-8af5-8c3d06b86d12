const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { createServer } = require('http');
const { Server } = require('socket.io');
const connectDB = require('./config/db');
const path = require('path');
const fs = require('fs');

// Load environment variables
dotenv.config();

// Connect to database
connectDB();

// Initialize Express app
const app = express();
const httpServer = createServer(app);

// Initialize Socket.IO with simplified configuration
const io = new Server(httpServer, {
  cors: {
    origin: [
      'http://localhost:50000',
      'http://localhost:50001',
      'http://localhost:50002',
      'http://localhost:50003',
      'http://localhost:50004',
      'http://localhost:3000',
      'http://localhost:3001',
      process.env.SOCKET_CORS_ORIGIN,
      process.env.CLIENT_URL
    ].filter(Boolean),
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket', 'polling'],
  path: '/socket.io'
});

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
// Dynamic CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    // List of allowed origins
    const allowedOrigins = [
      'http://localhost:50000',
      'http://localhost:50001',
      'http://localhost:50002',
      'http://localhost:50003',
      'http://localhost:50004',
      'http://localhost:3000',
      'http://localhost:3001',
      process.env.CLIENT_URL
    ].filter(Boolean);

    // Check if the origin is in the allowed list or matches localhost pattern
    const isLocalhost = /^http:\/\/localhost:\d+$/.test(origin);

    if (allowedOrigins.includes(origin) || isLocalhost) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
};

app.use(cors(corsOptions));
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" } // Allow images to be served from different origin
}));

// Logging middleware in development
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Set static folder for uploads
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('Created uploads directory');
}

// Create subdirectories for different types of uploads
const uploadSubdirs = ['posts', 'stories', 'reels', 'profiles', 'messages', 'products', 'misc'];
uploadSubdirs.forEach(subdir => {
  const subdirPath = path.join(uploadsDir, subdir);
  if (!fs.existsSync(subdirPath)) {
    fs.mkdirSync(subdirPath, { recursive: true });
    console.log(`Created uploads subdirectory: ${subdir}`);
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/users', require('./routes/users'));
app.use('/api/posts', require('./routes/posts'));
app.use('/api/stories', require('./routes/stories'));
app.use('/api/reels', require('./routes/reels'));
app.use('/api/comments', require('./routes/comments'));
app.use('/api/likes', require('./routes/likes'));
app.use('/api/follows', require('./routes/follows'));
app.use('/api/notifications', require('./routes/notifications'));
app.use('/api/test-notifications', require('./routes/test-notifications'));
app.use('/api/emotions', require('./routes/emotions'));
app.use('/api/moods', require('./routes/moods'));
app.use('/api/user-moods', require('./routes/userMoods'));
app.use('/api/ar', require('./routes/ar'));
app.use('/api/audio-cues', require('./routes/audioCues'));
app.use('/api/feed', require('./routes/feed'));
app.use('/api/content', require('./routes/content'));
app.use('/api/wellness', require('./routes/wellness'));
app.use('/api/tags', require('./routes/tags'));
app.use('/api/conversations', require('./routes/conversations'));
app.use('/api/messages', require('./routes/messages'));
app.use('/api/live-streams', require('./routes/liveStreams'));
app.use('/api/monetization', require('./routes/monetization'));
app.use('/api/users/me/reminders', require('./routes/reminders'));
app.use('/api/users/me/notification-preferences', require('./routes/notificationPreferences'));
app.use('/api/users/me/theme', require('./routes/theme'));
app.use('/api/shop', require('./routes/shop'));
app.use('/api/marketing', require('./routes/marketing'));
app.use('/api/loyalty', require('./routes/loyalty'));
app.use('/api/analytics', require('./routes/analytics'));
app.use('/api/vendors', require('./routes/vendors'));
app.use('/api/vendor-reviews', require('./routes/vendorReviews'));
app.use('/api/trust', require('./routes/trust'));
app.use('/api/payments', require('./routes/payments'));
app.use('/api/moderation', require('./routes/moderation'));
app.use('/api/safety', require('./routes/safety'));
app.use('/api/voice-commands', require('./routes/voiceCommands'));
app.use('/api/clips', require('./routes/streamClips'));
app.use('/api/live-streams/recordings', require('./routes/streamRecordings'));
app.use('/api/admin', require('./routes/admin'));

// Basic Socket.IO connection handling to avoid conflicts
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

console.log('✅ Socket.IO server initialized with basic configuration');

// Initialize message scheduler
const schedulerService = require('./services/schedulerService');
schedulerService.initScheduler();

// Error handling middleware
const errorHandler = require('./middleware/error');
app.use(errorHandler);

// Import utilities
const { findAvailablePort } = require('./utils/portFinder');
const { generateDynamicConfig } = require('./utils/dynamicConfig');

// Start server with port finding capability
const startServer = async () => {
  try {
    const preferredPort = parseInt(process.env.PORT || 10000);
    const PORT = await findAvailablePort(preferredPort);

    // If we're using a different port than what's in the .env file, log a warning
    if (PORT !== preferredPort) {
      console.warn(`⚠️ WARNING: Preferred port ${preferredPort} was in use. Using port ${PORT} instead.`);
      console.warn(`⚠️ Make sure your frontend is configured to connect to the correct port.`);

      // Update the environment variable so other parts of the app use the correct port
      process.env.PORT = PORT.toString();
    }

    httpServer.listen(PORT, () => {
      console.log(`✅ Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
      console.log(`📡 API available at http://localhost:${PORT}/api`);
      console.log(`🔌 Socket.IO available at http://localhost:${PORT}/socket.io`);

      // Generate dynamic configuration for the frontend
      generateDynamicConfig(PORT);
    });
  } catch (err) {
    console.error(`❌ Failed to start server: ${err.message}`);
    process.exit(1);
  }
};

// Call the async function to start the server
startServer();

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  httpServer.close(() => process.exit(1));
});
